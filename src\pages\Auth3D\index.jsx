import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { gsap } from "gsap";
import {
  FaEye, FaEyeSlash, FaUser, FaLock, FaCar, FaEnvelope, FaPhone,
  FaArrowLeft, FaCheckCircle, FaMoon, FaSun, FaCog
} from "react-icons/fa";
import ROUTES from "@constants/routes";

const Auth3D = () => {
  const [currentForm, setCurrentForm] = useState('login'); // 'login', 'register', 'forgot'
  const [isAnimating, setIsAnimating] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(true);
  const [formData, setFormData] = useState({
    email: "", password: "", firstName: "", lastName: "", phone: "",
    confirmPassword: "", resetEmail: ""
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [resetSuccess, setResetSuccess] = useState(false);

  const cubeRef = useRef(null);
  const containerRef = useRef(null);
  const particlesRef = useRef(null);
  const backgroundRef = useRef(null);
  const progressRef = useRef(null);
  const navigate = useNavigate();

  useEffect(() => {
    initializeScene();
    initializeParticles();
    startBackgroundAnimations();
  }, []);

  const initializeScene = () => {
    // Initial 3D cube setup and entrance animation
    const tl = gsap.timeline({ delay: 0.5 });

    // Cube entrance with 3D rotation
    tl.fromTo(cubeRef.current, {
      rotationY: -90,
      rotationX: 45,
      scale: 0.5,
      opacity: 0,
      z: -500
    }, {
      rotationY: 0,
      rotationX: 0,
      scale: 1,
      opacity: 1,
      z: 0,
      duration: 2,
      ease: "power4.out"
    });

    // Floating animation for the cube
    gsap.to(cubeRef.current, {
      y: -15,
      duration: 4,
      ease: "power2.inOut",
      repeat: -1,
      yoyo: true
    });

    // Subtle rotation animation
    gsap.to(cubeRef.current, {
      rotationY: 5,
      rotationX: 2,
      duration: 8,
      ease: "power2.inOut",
      repeat: -1,
      yoyo: true
    });
  };

  const initializeParticles = () => {
    // Create floating particles
    const particleCount = 50;
    const particles = [];

    for (let i = 0; i < particleCount; i++) {
      const particle = document.createElement('div');
      particle.className = 'particle';
      particle.style.cssText = `
        position: absolute;
        width: ${Math.random() * 4 + 1}px;
        height: ${Math.random() * 4 + 1}px;
        background: ${Math.random() > 0.5 ? '#00ffff' : '#ff6b35'};
        border-radius: 50%;
        opacity: ${Math.random() * 0.8 + 0.2};
        left: ${Math.random() * 100}%;
        top: ${Math.random() * 100}%;
        pointer-events: none;
        box-shadow: 0 0 10px currentColor;
      `;

      particlesRef.current?.appendChild(particle);
      particles.push(particle);

      // Animate particles
      gsap.to(particle, {
        y: -window.innerHeight,
        x: `+=${Math.random() * 200 - 100}`,
        rotation: 360,
        duration: Math.random() * 10 + 5,
        repeat: -1,
        ease: "none"
      });
    }
  };

  const startBackgroundAnimations = () => {
    // Animated gradient background
    gsap.to(backgroundRef.current, {
      backgroundPosition: "200% 200%",
      duration: 20,
      ease: "none",
      repeat: -1
    });

    // Pulsing glow effect
    gsap.to(".glow-effect", {
      scale: 1.2,
      opacity: 0.8,
      duration: 3,
      ease: "power2.inOut",
      repeat: -1,
      yoyo: true
    });
  };

  const rotateCube = (targetForm, direction = 'Y') => {
    if (isAnimating || currentForm === targetForm) return;

    setIsAnimating(true);
    setErrors({});
    setResetSuccess(false);

    const rotationMap = {
      login: { rotationY: 0, rotationX: 0 },
      register: { rotationY: direction === 'Y' ? -90 : 0, rotationX: direction === 'X' ? -90 : 0 },
      forgot: { rotationY: direction === 'Y' ? 90 : 0, rotationX: direction === 'X' ? 90 : 0 }
    };

    const tl = gsap.timeline({
      onComplete: () => {
        setCurrentForm(targetForm);
        setIsAnimating(false);
      }
    });

    // Update progress indicator
    updateProgress(targetForm);

    // 3D rotation with advanced easing
    tl.to(cubeRef.current, {
      ...rotationMap[targetForm],
      duration: 1.2,
      ease: "power4.inOut",
      transformOrigin: "center center"
    });

    // Add particle burst effect
    createParticleBurst();
  };

  const updateProgress = (form) => {
    const progressMap = { login: 0, register: 1, forgot: 2 };
    const progress = progressMap[form];

    gsap.to(".progress-dot", {
      scale: 1,
      opacity: 0.5,
      duration: 0.3
    });

    gsap.to(`.progress-dot:nth-child(${progress + 1})`, {
      scale: 1.5,
      opacity: 1,
      duration: 0.5,
      ease: "back.out(1.7)"
    });
  };

  const createParticleBurst = () => {
    const burstCount = 20;
    for (let i = 0; i < burstCount; i++) {
      const burst = document.createElement('div');
      burst.className = 'burst-particle';
      burst.style.cssText = `
        position: absolute;
        width: 6px;
        height: 6px;
        background: #00ffff;
        border-radius: 50%;
        left: 50%;
        top: 50%;
        pointer-events: none;
        box-shadow: 0 0 15px #00ffff;
      `;

      cubeRef.current?.appendChild(burst);

      const angle = (i / burstCount) * Math.PI * 2;
      const distance = 100 + Math.random() * 50;

      gsap.to(burst, {
        x: Math.cos(angle) * distance,
        y: Math.sin(angle) * distance,
        opacity: 0,
        scale: 0,
        duration: 1,
        ease: "power2.out",
        onComplete: () => burst.remove()
      });
    }
  };

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);

    const tl = gsap.timeline();

    // Theme transition animation
    tl.to(containerRef.current, {
      scale: 0.95,
      duration: 0.3,
      ease: "power2.inOut"
    })
    .to(containerRef.current, {
      scale: 1,
      duration: 0.3,
      ease: "power2.inOut"
    });

    // Update CSS custom properties
    document.documentElement.style.setProperty(
      '--theme-bg',
      isDarkMode ? '#f0f0f0' : '#0a0a0a'
    );
    document.documentElement.style.setProperty(
      '--theme-text',
      isDarkMode ? '#333' : '#fff'
    );
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: "" }));
    }

    // Real-time validation animation
    const input = e.target;
    gsap.to(input, {
      borderColor: value ? '#00ffff' : '#333',
      boxShadow: value ? '0 0 20px rgba(0, 255, 255, 0.3)' : 'none',
      duration: 0.3
    });
  };

  const validateForm = () => {
    const newErrors = {};

    if (currentForm === 'login') {
      if (!formData.email) newErrors.email = "Email is required";
      if (!formData.password) newErrors.password = "Password is required";
    } else if (currentForm === 'register') {
      if (!formData.firstName) newErrors.firstName = "First name is required";
      if (!formData.lastName) newErrors.lastName = "Last name is required";
      if (!formData.email) newErrors.email = "Email is required";
      if (!formData.phone) newErrors.phone = "Phone is required";
      if (!formData.password) newErrors.password = "Password is required";
      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = "Passwords do not match";
      }
    } else if (currentForm === 'forgot') {
      if (!formData.resetEmail) newErrors.resetEmail = "Email is required";
    }

    setErrors(newErrors);

    // Error animation
    if (Object.keys(newErrors).length > 0) {
      gsap.to(cubeRef.current, {
        x: [-10, 10, -10, 10, 0],
        duration: 0.5,
        ease: "power2.out"
      });
    }

    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsLoading(true);

    // Loading animation
    gsap.to(cubeRef.current, {
      rotationY: "+=360",
      duration: 2,
      ease: "power2.inOut"
    });

    try {
      await new Promise(resolve => setTimeout(resolve, 2000));

      if (currentForm === 'forgot') {
        setResetSuccess(true);
        gsap.fromTo(".success-content", {
          scale: 0.5,
          opacity: 0,
          rotationY: 180
        }, {
          scale: 1,
          opacity: 1,
          rotationY: 0,
          duration: 1,
          ease: "back.out(1.7)"
        });
      } else {
        // Success particle explosion
        createSuccessExplosion();

        setTimeout(() => {
          if (currentForm === 'login') {
            navigate(ROUTES.DASHBOARD);
          } else {
            rotateCube('login');
          }
        }, 1500);
      }
    } catch (error) {
      console.error("Form submission failed:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const createSuccessExplosion = () => {
    const explosionCount = 50;
    for (let i = 0; i < explosionCount; i++) {
      const particle = document.createElement('div');
      particle.style.cssText = `
        position: absolute;
        width: 8px;
        height: 8px;
        background: #00ff00;
        border-radius: 50%;
        left: 50%;
        top: 50%;
        pointer-events: none;
        box-shadow: 0 0 20px #00ff00;
      `;

      cubeRef.current?.appendChild(particle);

      const angle = (i / explosionCount) * Math.PI * 2;
      const distance = 200 + Math.random() * 100;

      gsap.to(particle, {
        x: Math.cos(angle) * distance,
        y: Math.sin(angle) * distance,
        opacity: 0,
        scale: 0,
        duration: 2,
        ease: "power3.out",
        onComplete: () => particle.remove()
      });
    }
  };

  const togglePasswordVisibility = (field) => {
    if (field === 'password') {
      setShowPassword(!showPassword);
    } else {
      setShowConfirmPassword(!showConfirmPassword);
    }

    gsap.to(`.${field}-icon`, {
      rotationY: 180,
      duration: 0.3,
      ease: "power2.out"
    });
  };

  return (
    <div
      className={`auth-3d-layout ${isDarkMode ? 'dark' : 'light'}`}
      ref={containerRef}
    >
      {/* Animated Background */}
      <div className="animated-background" ref={backgroundRef}>
        <div className="glow-effect"></div>
        <div className="grid-overlay"></div>
      </div>

      {/* Floating Particles */}
      <div className="particles-container" ref={particlesRef}></div>

      {/* Theme Toggle */}
      <button className="theme-toggle" onClick={toggleTheme}>
        {isDarkMode ? <FaSun /> : <FaMoon />}
      </button>

      {/* Progress Indicator */}
      <div className="progress-indicator" ref={progressRef}>
        <div className="progress-dot active"></div>
        <div className="progress-dot"></div>
        <div className="progress-dot"></div>
      </div>

      {/* 3D Authentication Cube */}
      <div className="auth-3d-container">
        <div className="auth-cube" ref={cubeRef}>
          {/* Login Face */}
          <div className={`cube-face login-face ${currentForm === 'login' ? 'active' : ''}`}>
            <div className="form-content">
              <div className="logo-section">
                <FaCar className="floating-logo" />
                <h1>AutoService</h1>
                <p>Next-Gen Vehicle Management</p>
              </div>

              <form onSubmit={handleSubmit} className="auth-form-3d">
                <div className="floating-input">
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                  />
                  <label>Email Address</label>
                  <FaEnvelope className="input-icon" />
                </div>

                <div className="floating-input">
                  <input
                    type={showPassword ? "text" : "password"}
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    required
                  />
                  <label>Password</label>
                  <FaLock className="input-icon" />
                  <span
                    className="password-toggle password-icon"
                    onClick={() => togglePasswordVisibility('password')}
                  >
                    {showPassword ? <FaEyeSlash /> : <FaEye />}
                  </span>
                </div>

                <button type="submit" className="submit-btn-3d" disabled={isLoading}>
                  {isLoading ? <div className="loading-spinner-3d"></div> : "Sign In"}
                </button>
              </form>

              <div className="form-links-3d">
                <button
                  type="button"
                  onClick={() => rotateCube('forgot', 'Y')}
                  className="link-btn"
                >
                  Forgot Password?
                </button>
                <button
                  type="button"
                  onClick={() => rotateCube('register', 'Y')}
                  className="link-btn primary"
                >
                  Create Account
                </button>
              </div>
            </div>
          </div>

          {/* Register Face */}
          <div className={`cube-face register-face ${currentForm === 'register' ? 'active' : ''}`}>
            <div className="form-content">
              <div className="logo-section">
                <FaCar className="floating-logo" />
                <h1>Join AutoService</h1>
                <p>Professional Network</p>
              </div>

              <form onSubmit={handleSubmit} className="auth-form-3d">
                <div className="input-row">
                  <div className="floating-input">
                    <input
                      type="text"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleInputChange}
                      required
                    />
                    <label>First Name</label>
                    <FaUser className="input-icon" />
                  </div>

                  <div className="floating-input">
                    <input
                      type="text"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      required
                    />
                    <label>Last Name</label>
                    <FaUser className="input-icon" />
                  </div>
                </div>

                <div className="floating-input">
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                  />
                  <label>Email Address</label>
                  <FaEnvelope className="input-icon" />
                </div>

                <div className="floating-input">
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    required
                  />
                  <label>Phone Number</label>
                  <FaPhone className="input-icon" />
                </div>

                <div className="floating-input">
                  <input
                    type={showPassword ? "text" : "password"}
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    required
                  />
                  <label>Password</label>
                  <FaLock className="input-icon" />
                  <span
                    className="password-toggle password-icon"
                    onClick={() => togglePasswordVisibility('password')}
                  >
                    {showPassword ? <FaEyeSlash /> : <FaEye />}
                  </span>
                </div>

                <div className="floating-input">
                  <input
                    type={showConfirmPassword ? "text" : "password"}
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    required
                  />
                  <label>Confirm Password</label>
                  <FaLock className="input-icon" />
                  <span
                    className="password-toggle confirmPassword-icon"
                    onClick={() => togglePasswordVisibility('confirmPassword')}
                  >
                    {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
                  </span>
                </div>

                <button type="submit" className="submit-btn-3d" disabled={isLoading}>
                  {isLoading ? <div className="loading-spinner-3d"></div> : "Create Account"}
                </button>
              </form>

              <div className="form-links-3d">
                <button
                  type="button"
                  onClick={() => rotateCube('login', 'Y')}
                  className="link-btn"
                >
                  Back to Sign In
                </button>
              </div>
            </div>
          </div>

          {/* Forgot Password Face */}
          <div className={`cube-face forgot-face ${currentForm === 'forgot' ? 'active' : ''}`}>
            <div className="form-content">
              {!resetSuccess ? (
                <>
                  <div className="logo-section">
                    <FaCar className="floating-logo" />
                    <h1>Reset Password</h1>
                    <p>Secure Recovery</p>
                  </div>

                  <form onSubmit={handleSubmit} className="auth-form-3d">
                    <div className="floating-input">
                      <input
                        type="email"
                        name="resetEmail"
                        value={formData.resetEmail}
                        onChange={handleInputChange}
                        required
                      />
                      <label>Email Address</label>
                      <FaEnvelope className="input-icon" />
                    </div>

                    <button type="submit" className="submit-btn-3d" disabled={isLoading}>
                      {isLoading ? <div className="loading-spinner-3d"></div> : "Send Reset Link"}
                    </button>
                  </form>

                  <div className="form-links-3d">
                    <button
                      type="button"
                      onClick={() => rotateCube('login', 'Y')}
                      className="link-btn"
                    >
                      <FaArrowLeft /> Back to Sign In
                    </button>
                    <button
                      type="button"
                      onClick={() => rotateCube('register', 'Y')}
                      className="link-btn"
                    >
                      Create Account
                    </button>
                  </div>
                </>
              ) : (
                <div className="success-content">
                  <FaCheckCircle className="success-icon" />
                  <h2>Reset Link Sent!</h2>
                  <p>Check your email: <strong>{formData.resetEmail}</strong></p>
                  <button
                    onClick={() => rotateCube('login', 'Y')}
                    className="submit-btn-3d"
                  >
                    Back to Sign In
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Auth3D;
