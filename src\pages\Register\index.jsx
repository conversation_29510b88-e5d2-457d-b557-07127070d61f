import React, { useState, useEffect, useRef } from "react";
import { Link, useNavigate } from "react-router-dom";
import { gsap } from "gsap";
import { FaEye, FaEyeSlash, FaUser, FaLock, FaCar, FaEnvelope, FaPhone } from "react-icons/fa";
import ROUTES from "@constants/routes";

const Register = () => {
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    password: "",
    confirmPassword: ""
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const cardRef = useRef(null);
  const logoRef = useRef(null);
  const formRef = useRef(null);
  const linksRef = useRef(null);
  const navigate = useNavigate();

  useEffect(() => {
    // GSAP entrance animations
    const tl = gsap.timeline({ delay: 0.2 });

    // Animate card entrance
    tl.fromTo(cardRef.current,
      { scale: 0.8, opacity: 0, y: 50 },
      { scale: 1, opacity: 1, y: 0, duration: 0.8, ease: "back.out(1.7)" }
    );

    // Animate logo
    tl.fromTo(logoRef.current,
      { y: -30, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.6, ease: "power2.out" },
      "-=0.4"
    );

    // Animate form elements
    tl.fromTo(formRef.current.children,
      { x: -30, opacity: 0 },
      { x: 0, opacity: 1, duration: 0.5, stagger: 0.1, ease: "power2.out" },
      "-=0.3"
    );

    // Animate links
    tl.fromTo(linksRef.current,
      { y: 20, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.5, ease: "power2.out" },
      "-=0.2"
    );

    // Floating animation for the car icon
    gsap.to(".car-icon", {
      y: -10,
      duration: 2,
      ease: "power2.inOut",
      repeat: -1,
      yoyo: true
    });

  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ""
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.firstName) {
      newErrors.firstName = "First name is required";
    }
    
    if (!formData.lastName) {
      newErrors.lastName = "Last name is required";
    }
    
    if (!formData.email) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email is invalid";
    }
    
    if (!formData.phone) {
      newErrors.phone = "Phone number is required";
    } else if (!/^\d{10}$/.test(formData.phone.replace(/\D/g, ''))) {
      newErrors.phone = "Phone number must be 10 digits";
    }
    
    if (!formData.password) {
      newErrors.password = "Password is required";
    } else if (formData.password.length < 6) {
      newErrors.password = "Password must be at least 6 characters";
    }
    
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = "Please confirm your password";
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      // Shake animation for errors
      gsap.to(cardRef.current, {
        x: [-10, 10, -10, 10, 0],
        duration: 0.5,
        ease: "power2.out"
      });
      return;
    }

    setIsLoading(true);
    
    // Simulate API call
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Success animation
      gsap.to(cardRef.current, {
        scale: 1.05,
        duration: 0.3,
        ease: "power2.out",
        yoyo: true,
        repeat: 1
      });
      
      // Navigate to login
      setTimeout(() => {
        navigate(ROUTES.LOGIN);
      }, 600);
      
    } catch (error) {
      console.error("Registration failed:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const togglePasswordVisibility = (field) => {
    if (field === 'password') {
      setShowPassword(!showPassword);
    } else {
      setShowConfirmPassword(!showConfirmPassword);
    }
    
    // Icon animation
    gsap.to(`.${field}-icon`, {
      scale: 0.8,
      duration: 0.1,
      yoyo: true,
      repeat: 1,
      ease: "power2.out"
    });
  };

  return (
    <div className="auth-card" ref={cardRef}>
      <div className="auth-logo" ref={logoRef}>
        <FaCar className="car-icon" style={{ fontSize: "3rem", color: "var(--primary-orange)", marginBottom: "10px" }} />
        <h1>AutoService</h1>
        <p>Join Our Professional Network</p>
      </div>

      <form className="auth-form" onSubmit={handleSubmit} ref={formRef}>
        <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "15px" }}>
          <div className="form-group">
            <label className="form-label" htmlFor="firstName">
              <FaUser style={{ marginRight: "8px" }} />
              First Name
            </label>
            <input
              type="text"
              id="firstName"
              name="firstName"
              className="form-control"
              placeholder="First name"
              value={formData.firstName}
              onChange={handleInputChange}
              autoComplete="given-name"
            />
            {errors.firstName && (
              <div className="error-message">
                <span>⚠️</span>
                {errors.firstName}
              </div>
            )}
          </div>

          <div className="form-group">
            <label className="form-label" htmlFor="lastName">
              Last Name
            </label>
            <input
              type="text"
              id="lastName"
              name="lastName"
              className="form-control"
              placeholder="Last name"
              value={formData.lastName}
              onChange={handleInputChange}
              autoComplete="family-name"
            />
            {errors.lastName && (
              <div className="error-message">
                <span>⚠️</span>
                {errors.lastName}
              </div>
            )}
          </div>
        </div>

        <div className="form-group">
          <label className="form-label" htmlFor="email">
            <FaEnvelope style={{ marginRight: "8px" }} />
            Email Address
          </label>
          <input
            type="email"
            id="email"
            name="email"
            className="form-control"
            placeholder="Enter your email"
            value={formData.email}
            onChange={handleInputChange}
            autoComplete="email"
          />
          {errors.email && (
            <div className="error-message">
              <span>⚠️</span>
              {errors.email}
            </div>
          )}
        </div>

        <div className="form-group">
          <label className="form-label" htmlFor="phone">
            <FaPhone style={{ marginRight: "8px" }} />
            Phone Number
          </label>
          <input
            type="tel"
            id="phone"
            name="phone"
            className="form-control"
            placeholder="Enter your phone number"
            value={formData.phone}
            onChange={handleInputChange}
            autoComplete="tel"
          />
          {errors.phone && (
            <div className="error-message">
              <span>⚠️</span>
              {errors.phone}
            </div>
          )}
        </div>

        <div className="form-group">
          <label className="form-label" htmlFor="password">
            <FaLock style={{ marginRight: "8px" }} />
            Password
          </label>
          <div className="input-group">
            <input
              type={showPassword ? "text" : "password"}
              id="password"
              name="password"
              className="form-control"
              placeholder="Create a password"
              value={formData.password}
              onChange={handleInputChange}
              autoComplete="new-password"
            />
            <span 
              className="input-icon password-icon" 
              onClick={() => togglePasswordVisibility('password')}
            >
              {showPassword ? <FaEyeSlash /> : <FaEye />}
            </span>
          </div>
          {errors.password && (
            <div className="error-message">
              <span>⚠️</span>
              {errors.password}
            </div>
          )}
        </div>

        <div className="form-group">
          <label className="form-label" htmlFor="confirmPassword">
            Confirm Password
          </label>
          <div className="input-group">
            <input
              type={showConfirmPassword ? "text" : "password"}
              id="confirmPassword"
              name="confirmPassword"
              className="form-control"
              placeholder="Confirm your password"
              value={formData.confirmPassword}
              onChange={handleInputChange}
              autoComplete="new-password"
            />
            <span 
              className="input-icon confirmPassword-icon" 
              onClick={() => togglePasswordVisibility('confirmPassword')}
            >
              {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
            </span>
          </div>
          {errors.confirmPassword && (
            <div className="error-message">
              <span>⚠️</span>
              {errors.confirmPassword}
            </div>
          )}
        </div>

        <button 
          type="submit" 
          className="btn-primary"
          disabled={isLoading}
        >
          {isLoading && <div className="loading-spinner"></div>}
          {isLoading ? "Creating Account..." : "Create Account"}
        </button>
      </form>

      <div className="auth-links" ref={linksRef}>
        <div className="auth-divider">
          <span>Already have an account?</span>
        </div>
        
        <Link to={ROUTES.LOGIN} className="btn-secondary">
          Sign In
        </Link>
      </div>
    </div>
  );
};

export default Register;
