import React, { useState, useEffect, useRef } from "react";
import { Link, useNavigate } from "react-router-dom";
import { gsap } from "gsap";
import { FaEnvelope, FaCar, FaArrowLeft, FaCheckCircle } from "react-icons/fa";
import ROUTES from "@constants/routes";

const ForgotPassword = () => {
  const [formData, setFormData] = useState({
    email: ""
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [errors, setErrors] = useState({});

  const cardRef = useRef(null);
  const logoRef = useRef(null);
  const formRef = useRef(null);
  const linksRef = useRef(null);
  const successRef = useRef(null);
  const navigate = useNavigate();

  useEffect(() => {
    // GSAP entrance animations
    const tl = gsap.timeline({ delay: 0.2 });

    // Animate card entrance
    tl.fromTo(cardRef.current,
      { scale: 0.8, opacity: 0, y: 50 },
      { scale: 1, opacity: 1, y: 0, duration: 0.8, ease: "back.out(1.7)" }
    );

    // Animate logo
    tl.fromTo(logoRef.current,
      { y: -30, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.6, ease: "power2.out" },
      "-=0.4"
    );

    // Animate form elements
    tl.fromTo(formRef.current.children,
      { x: -30, opacity: 0 },
      { x: 0, opacity: 1, duration: 0.5, stagger: 0.1, ease: "power2.out" },
      "-=0.3"
    );

    // Animate links
    tl.fromTo(linksRef.current,
      { y: 20, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.5, ease: "power2.out" },
      "-=0.2"
    );

    // Floating animation for the car icon
    gsap.to(".car-icon", {
      y: -10,
      duration: 2,
      ease: "power2.inOut",
      repeat: -1,
      yoyo: true
    });

  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ""
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.email) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email is invalid";
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      // Shake animation for errors
      gsap.to(cardRef.current, {
        x: [-10, 10, -10, 10, 0],
        duration: 0.5,
        ease: "power2.out"
      });
      return;
    }

    setIsLoading(true);
    
    // Simulate API call
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Hide form and show success message
      gsap.to(formRef.current, {
        opacity: 0,
        y: -20,
        duration: 0.5,
        ease: "power2.out",
        onComplete: () => {
          setIsSuccess(true);
          // Show success message
          gsap.fromTo(successRef.current,
            { opacity: 0, y: 20 },
            { opacity: 1, y: 0, duration: 0.6, ease: "power2.out" }
          );
        }
      });
      
    } catch (error) {
      console.error("Password reset failed:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToLogin = () => {
    // Animate out and navigate
    gsap.to(cardRef.current, {
      scale: 0.9,
      opacity: 0,
      duration: 0.5,
      ease: "power2.out",
      onComplete: () => {
        navigate(ROUTES.LOGIN);
      }
    });
  };

  return (
    <div className="auth-card" ref={cardRef}>
      <div className="auth-logo" ref={logoRef}>
        <FaCar className="car-icon" style={{ fontSize: "3rem", color: "var(--primary-orange)", marginBottom: "10px" }} />
        <h1>AutoService</h1>
        <p>Reset Your Password</p>
      </div>

      {!isSuccess ? (
        <>
          <form className="auth-form" onSubmit={handleSubmit} ref={formRef}>
            <div style={{ textAlign: "center", marginBottom: "25px" }}>
              <p style={{ color: "var(--dark-silver)", fontSize: "0.95rem", lineHeight: "1.5" }}>
                Enter your email address and we'll send you a link to reset your password.
              </p>
            </div>

            <div className="form-group">
              <label className="form-label" htmlFor="email">
                <FaEnvelope style={{ marginRight: "8px" }} />
                Email Address
              </label>
              <input
                type="email"
                id="email"
                name="email"
                className="form-control"
                placeholder="Enter your email"
                value={formData.email}
                onChange={handleInputChange}
                autoComplete="email"
                autoFocus
              />
              {errors.email && (
                <div className="error-message">
                  <span>⚠️</span>
                  {errors.email}
                </div>
              )}
            </div>

            <button 
              type="submit" 
              className="btn-primary"
              disabled={isLoading}
            >
              {isLoading && <div className="loading-spinner"></div>}
              {isLoading ? "Sending Reset Link..." : "Send Reset Link"}
            </button>
          </form>

          <div className="auth-links" ref={linksRef}>
            <div style={{ textAlign: "center", marginTop: "25px" }}>
              <button 
                onClick={handleBackToLogin}
                style={{
                  background: "none",
                  border: "none",
                  color: "var(--primary-orange)",
                  textDecoration: "none",
                  fontWeight: "600",
                  cursor: "pointer",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  gap: "8px",
                  width: "100%",
                  padding: "10px",
                  borderRadius: "8px",
                  transition: "all 0.3s ease"
                }}
                onMouseEnter={(e) => {
                  e.target.style.backgroundColor = "rgba(255, 107, 53, 0.1)";
                }}
                onMouseLeave={(e) => {
                  e.target.style.backgroundColor = "transparent";
                }}
              >
                <FaArrowLeft />
                Back to Sign In
              </button>
            </div>
            
            <div className="auth-divider">
              <span>Don't have an account?</span>
            </div>
            
            <Link to={ROUTES.REGISTER} className="btn-secondary">
              Create Account
            </Link>
          </div>
        </>
      ) : (
        <div ref={successRef} style={{ textAlign: "center", opacity: 0 }}>
          <div style={{ marginBottom: "30px" }}>
            <FaCheckCircle 
              style={{ 
                fontSize: "4rem", 
                color: "var(--success-green)", 
                marginBottom: "20px",
                animation: "pulse 2s infinite"
              }} 
            />
            <h3 style={{ color: "var(--primary-black)", marginBottom: "15px" }}>
              Reset Link Sent!
            </h3>
            <p style={{ color: "var(--dark-silver)", fontSize: "0.95rem", lineHeight: "1.5" }}>
              We've sent a password reset link to <strong>{formData.email}</strong>. 
              Please check your email and follow the instructions to reset your password.
            </p>
          </div>

          <div style={{ marginTop: "30px" }}>
            <p style={{ color: "var(--dark-silver)", fontSize: "0.9rem", marginBottom: "20px" }}>
              Didn't receive the email? Check your spam folder or try again.
            </p>
            
            <button 
              onClick={() => {
                setIsSuccess(false);
                setFormData({ email: "" });
                // Animate form back in
                gsap.fromTo(formRef.current,
                  { opacity: 0, y: 20 },
                  { opacity: 1, y: 0, duration: 0.6, ease: "power2.out" }
                );
              }}
              className="btn-secondary"
              style={{ marginBottom: "15px" }}
            >
              Try Again
            </button>
            
            <button 
              onClick={handleBackToLogin}
              className="btn-primary"
            >
              Back to Sign In
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ForgotPassword;
