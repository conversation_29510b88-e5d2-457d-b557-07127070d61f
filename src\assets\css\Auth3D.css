/* 3D Authentication Interface - Futuristic Automotive Theme */

:root {
  /* Futuristic Color Palette */
  --neon-blue: #00ffff;
  --electric-orange: #ff6b35;
  --lime-green: #39ff14;
  --deep-black: #0a0a0a;
  --dark-gray: #1a1a1a;
  --silver-metallic: #c0c0c0;
  --neon-purple: #bf00ff;
  --electric-yellow: #ffff00;

  /* Theme Variables */
  --theme-bg: #0a0a0a;
  --theme-text: #ffffff;
  --theme-accent: #00ffff;
  --theme-secondary: #ff6b35;
}

/* Base Layout */
.auth-3d-layout {
  min-height: 100vh;
  background: var(--theme-bg);
  color: var(--theme-text);
  overflow: hidden;
  position: relative;
  perspective: 1000px;
  font-family: 'Orbitron', 'Segoe UI', monospace;
}

.auth-3d-layout.light {
  --theme-bg: #f0f0f0;
  --theme-text: #333333;
  --theme-accent: #0066cc;
  --theme-secondary: #ff4500;
}

/* Animated Background */
.animated-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 107, 53, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(57, 255, 20, 0.05) 0%, transparent 50%),
    linear-gradient(135deg, var(--deep-black) 0%, var(--dark-gray) 50%, var(--deep-black) 100%);
  background-size: 200% 200%;
  animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
  0%, 100% { background-position: 0% 0%; }
  50% { background-position: 100% 100%; }
}

.glow-effect {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 800px;
  height: 800px;
  transform: translate(-50%, -50%);
  background: radial-gradient(circle, rgba(0, 255, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  animation: pulse 4s ease-in-out infinite;
}

.grid-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 30s linear infinite;
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

/* Particles */
.particles-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.particle {
  position: absolute;
  border-radius: 50%;
  animation: float 10s linear infinite;
}

@keyframes float {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

/* Theme Toggle */
.theme-toggle {
  position: fixed;
  top: 30px;
  right: 30px;
  width: 60px;
  height: 60px;
  border: none;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--neon-blue), var(--electric-orange));
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  z-index: 1000;
  transition: all 0.3s ease;
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
}

.theme-toggle:hover {
  transform: scale(1.1) rotate(180deg);
  box-shadow: 0 0 30px rgba(0, 255, 255, 0.8);
}

/* Progress Indicator */
.progress-indicator {
  position: fixed;
  top: 50%;
  left: 30px;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 20px;
  z-index: 1000;
}

.progress-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: all 0.5s ease;
  position: relative;
}

.progress-dot.active,
.progress-dot:nth-child(1) {
  background: var(--neon-blue);
  box-shadow: 0 0 15px var(--neon-blue);
  transform: scale(1.5);
}

.progress-dot::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  border: 2px solid var(--neon-blue);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  animation: ripple 2s infinite;
}

@keyframes ripple {
  0% {
    transform: translate(-50%, -50%) scale(0.5);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

/* 3D Container */
.auth-3d-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  perspective: 1200px;
  z-index: 10;
}

.auth-cube {
  width: 450px;
  height: 600px;
  position: relative;
  transform-style: preserve-3d;
  transition: transform 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Cube Faces */
.cube-face {
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(26, 26, 26, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 20px;
  padding: 40px;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    0 0 30px rgba(0, 255, 255, 0.2);
  overflow: hidden;
  opacity: 0.3;
  transition: opacity 0.6s ease;
}

.cube-face.active {
  opacity: 1;
}

.cube-face::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg,
    var(--neon-blue) 0%,
    var(--electric-orange) 50%,
    var(--lime-green) 100%);
  animation: shimmer 3s ease-in-out infinite;
}

.login-face {
  transform: rotateY(0deg) translateZ(225px);
}

.register-face {
  transform: rotateY(-90deg) translateZ(225px);
}

.forgot-face {
  transform: rotateY(90deg) translateZ(225px);
}

/* Form Content */
.form-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.logo-section {
  text-align: center;
  margin-bottom: 30px;
}

.floating-logo {
  font-size: 4rem;
  color: var(--neon-blue);
  margin-bottom: 20px;
  animation: logoFloat 3s ease-in-out infinite;
  filter: drop-shadow(0 0 20px var(--neon-blue));
}

@keyframes logoFloat {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(5deg); }
}

.logo-section h1 {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, var(--neon-blue), var(--electric-orange));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 10px;
  text-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
}

.logo-section p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
  font-weight: 300;
  letter-spacing: 2px;
  text-transform: uppercase;
}

/* 3D Form Styles */
.auth-form-3d {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.input-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.floating-input {
  position: relative;
  margin-bottom: 20px;
}

.floating-input input {
  width: 100%;
  padding: 18px 50px 18px 20px;
  background: rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(0, 255, 255, 0.3);
  border-radius: 15px;
  color: white;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  backdrop-filter: blur(10px);
}

.floating-input input:focus {
  outline: none;
  border-color: var(--neon-blue);
  box-shadow:
    0 0 25px rgba(0, 255, 255, 0.4),
    inset 0 0 20px rgba(0, 255, 255, 0.1);
  transform: translateY(-3px);
}

.floating-input input:valid {
  border-color: var(--lime-green);
}

.floating-input label {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.7);
  font-size: 1rem;
  font-weight: 500;
  pointer-events: none;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, var(--neon-blue), var(--electric-orange));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.floating-input input:focus + label,
.floating-input input:valid + label {
  top: -10px;
  left: 15px;
  font-size: 0.85rem;
  color: var(--neon-blue);
  text-shadow: 0 0 10px var(--neon-blue);
}

.input-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--neon-blue);
  font-size: 1.2rem;
  pointer-events: none;
  filter: drop-shadow(0 0 5px var(--neon-blue));
}

.password-toggle {
  position: absolute;
  right: 50px;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.1rem;
  cursor: pointer;
  pointer-events: all;
  transition: all 0.3s ease;
}

.password-toggle:hover {
  color: var(--neon-blue);
  transform: translateY(-50%) scale(1.2);
}

/* 3D Buttons */
.submit-btn-3d {
  width: 100%;
  padding: 18px;
  background: linear-gradient(135deg, var(--neon-blue), var(--electric-orange));
  border: none;
  border-radius: 15px;
  color: white;
  font-size: 1.1rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 2px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.4s ease;
  box-shadow:
    0 10px 30px rgba(0, 255, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.submit-btn-3d::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent);
  transition: left 0.6s ease;
}

.submit-btn-3d:hover {
  transform: translateY(-3px);
  box-shadow:
    0 15px 40px rgba(0, 255, 255, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.submit-btn-3d:hover::before {
  left: 100%;
}

.submit-btn-3d:active {
  transform: translateY(-1px);
}

.submit-btn-3d:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* Loading Spinner */
.loading-spinner-3d {
  width: 24px;
  height: 24px;
  border: 3px solid transparent;
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin3d 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin3d {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Form Links */
.form-links-3d {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 20px;
}

.link-btn {
  background: none;
  border: 2px solid rgba(0, 255, 255, 0.3);
  border-radius: 10px;
  color: var(--neon-blue);
  padding: 12px 20px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.link-btn:hover {
  background: rgba(0, 255, 255, 0.1);
  border-color: var(--neon-blue);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 255, 255, 0.3);
}

.link-btn.primary {
  background: linear-gradient(135deg, var(--electric-orange), var(--neon-purple));
  border-color: var(--electric-orange);
  color: white;
}

.link-btn.primary:hover {
  background: linear-gradient(135deg, var(--neon-purple), var(--electric-orange));
  box-shadow: 0 5px 15px rgba(255, 107, 53, 0.4);
}

/* Success Content */
.success-content {
  text-align: center;
  padding: 40px 20px;
}

.success-icon {
  font-size: 5rem;
  color: var(--lime-green);
  margin-bottom: 30px;
  animation: successPulse 2s ease-in-out infinite;
  filter: drop-shadow(0 0 20px var(--lime-green));
}

@keyframes successPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.success-content h2 {
  font-size: 2rem;
  margin-bottom: 20px;
  background: linear-gradient(135deg, var(--lime-green), var(--neon-blue));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.success-content p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  margin-bottom: 30px;
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .auth-cube {
    width: 350px;
    height: 500px;
  }

  .cube-face {
    padding: 30px 25px;
  }

  .input-row {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .logo-section h1 {
    font-size: 2rem;
  }

  .floating-logo {
    font-size: 3rem;
  }

  .progress-indicator {
    left: 20px;
  }

  .theme-toggle {
    top: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .auth-cube {
    width: 300px;
    height: 450px;
  }

  .cube-face {
    padding: 25px 20px;
  }

  .logo-section h1 {
    font-size: 1.8rem;
  }

  .floating-input input {
    padding: 15px 45px 15px 18px;
    font-size: 0.95rem;
  }

  .submit-btn-3d {
    padding: 15px;
    font-size: 1rem;
  }
}

/* Advanced 3D Effects */
.auth-cube:hover {
  transform: rotateY(2deg) rotateX(1deg);
}

/* Burst Particles */
.burst-particle {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
  z-index: 1000;
}

/* Holographic Effect */
.cube-face::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg,
    transparent 30%,
    rgba(0, 255, 255, 0.1) 50%,
    transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.cube-face:hover::after {
  opacity: 1;
  animation: holographicSweep 2s ease-in-out infinite;
}

@keyframes holographicSweep {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Floating Input Enhancements */
.floating-input:hover input {
  border-color: rgba(0, 255, 255, 0.6);
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
}

/* Button Ripple Effect */
.submit-btn-3d::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.submit-btn-3d:active::after {
  width: 300px;
  height: 300px;
}

/* Cyberpunk Glow */
@keyframes cyberpunkGlow {
  0%, 100% {
    text-shadow:
      0 0 5px var(--neon-blue),
      0 0 10px var(--neon-blue),
      0 0 15px var(--neon-blue);
  }
  50% {
    text-shadow:
      0 0 10px var(--electric-orange),
      0 0 20px var(--electric-orange),
      0 0 30px var(--electric-orange);
  }
}

.logo-section h1 {
  animation: cyberpunkGlow 4s ease-in-out infinite;
}

/* Matrix Rain Effect */
.matrix-rain {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
  z-index: 1;
}

.matrix-char {
  position: absolute;
  color: var(--lime-green);
  font-family: 'Courier New', monospace;
  font-size: 14px;
  opacity: 0.7;
  animation: matrixFall 3s linear infinite;
}

@keyframes matrixFall {
  0% {
    transform: translateY(-100px);
    opacity: 0;
  }
  10% {
    opacity: 0.7;
  }
  90% {
    opacity: 0.7;
  }
  100% {
    transform: translateY(100vh);
    opacity: 0;
  }
}
