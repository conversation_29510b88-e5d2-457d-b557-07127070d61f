import React, { useEffect, useRef } from "react";
import { Outlet } from "react-router-dom";
import { gsap } from "gsap";
import { FaCar, FaWrench, FaShieldAlt } from "react-icons/fa";

const AuthLayout = () => {
  const layoutRef = useRef(null);
  const backgroundRef = useRef(null);
  const brandingRef = useRef(null);

  useEffect(() => {
    // GSAP animation for layout entrance
    const tl = gsap.timeline();

    // Animate layout entrance
    tl.fromTo(layoutRef.current,
      { opacity: 0 },
      { opacity: 1, duration: 1, ease: "power2.out" }
    );

    // Animate background elements
    tl.fromTo(backgroundRef.current.children,
      { scale: 0, opacity: 0, rotation: -180 },
      { scale: 1, opacity: 0.1, rotation: 0, duration: 1.5, stagger: 0.2, ease: "back.out(1.7)" },
      "-=0.8"
    );

    // Animate branding
    tl.fromTo(brandingRef.current,
      { y: -50, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.8, ease: "power2.out" },
      "-=0.5"
    );

    // Continuous floating animation for background icons
    gsap.to(".floating-icon", {
      y: "+=20",
      duration: 3,
      ease: "power2.inOut",
      repeat: -1,
      yoyo: true,
      stagger: 0.5
    });

  }, []);

  return (
    <div className="professional-auth-layout" ref={layoutRef}>
      {/* Background Elements */}
      <div className="auth-background" ref={backgroundRef}>
        <FaCar className="floating-icon icon-1" />
        <FaWrench className="floating-icon icon-2" />
        <FaShieldAlt className="floating-icon icon-3" />
        <FaCar className="floating-icon icon-4" />
        <FaWrench className="floating-icon icon-5" />
      </div>

      {/* Branding Section */}
      <div className="auth-branding" ref={brandingRef}>
        <div className="brand-logo">
          <FaCar className="brand-icon" />
          <h1>AutoService Pro</h1>
        </div>
        <p className="brand-tagline">Professional Vehicle Management System</p>
        <div className="brand-features">
          <span><FaWrench /> Service Management</span>
          <span><FaShieldAlt /> Quality Assurance</span>
          <span><FaCar /> Fleet Tracking</span>
        </div>
      </div>

      {/* Auth Content */}
      <div className="auth-content">
        <Outlet />
      </div>
    </div>
  );
};

export default AuthLayout;
