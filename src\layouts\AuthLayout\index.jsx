import React, { useEffect, useRef } from "react";
import { Outlet } from "react-router-dom";
import { gsap } from "gsap";

const AuthLayout = () => {
  const layoutRef = useRef(null);

  useEffect(() => {
    // GSAP animation for layout entrance
    const tl = gsap.timeline();

    tl.fromTo(layoutRef.current,
      { opacity: 0 },
      { opacity: 1, duration: 0.8, ease: "power2.out" }
    );

    // Animate background pattern
    gsap.to(".auth-layout::before", {
      opacity: 0.2,
      duration: 2,
      ease: "power2.inOut",
      repeat: -1,
      yoyo: true
    });

  }, []);

  return (
    <div className="auth-layout" ref={layoutRef}>
      <div className="auth-container">
        <Outlet />
      </div>
    </div>
  );
};

export default AuthLayout;
