import React, { useEffect, useRef } from "react";
import { Outlet } from "react-router-dom";
import { gsap } from "gsap";

const AuthLayout = () => {
  const layoutRef = useRef(null);
  const leftPanelRef = useRef(null);
  const rightPanelRef = useRef(null);

  useEffect(() => {
    // GSAP animation for layout entrance
    const tl = gsap.timeline();

    // Animate layout entrance
    tl.fromTo(layoutRef.current,
      { opacity: 0 },
      { opacity: 1, duration: 0.8, ease: "power2.out" }
    );

    // Animate left panel
    tl.fromTo(leftPanelRef.current,
      { x: -100, opacity: 0 },
      { x: 0, opacity: 1, duration: 0.8, ease: "power2.out" },
      "-=0.4"
    );

    // Animate right panel
    tl.fromTo(rightPanelRef.current,
      { x: 100, opacity: 0 },
      { x: 0, opacity: 1, duration: 0.8, ease: "power2.out" },
      "-=0.6"
    );

  }, []);

  return (
    <div className="clean-auth-layout" ref={layoutRef}>
      {/* Left Panel - Branding/Video */}
      <div className="auth-left-panel" ref={leftPanelRef}>
        <div className="brand-content">
          <div className="brand-header">
            <h1>AutoService Pro</h1>
            <p>Professional Vehicle Management System</p>
          </div>

          {/* Video Background */}
          <div className="video-container">
            <video
              autoPlay
              muted
              loop
              playsInline
              className="background-video"
            >
              <source src="https://player.vimeo.com/external/371433846.sd.mp4?s=236da2f3c0fd273d2c6d9a064f3ae35579b2bbdf&profile_id=139&oauth2_token_id=57447761" type="video/mp4" />
              {/* Fallback image if video doesn't load */}
            </video>
            <div className="video-overlay"></div>
          </div>

          {/* Features */}
          <div className="features-list">
            <div className="feature-item">
              <span className="feature-icon">🚗</span>
              <span>Fleet Management</span>
            </div>
            <div className="feature-item">
              <span className="feature-icon">🔧</span>
              <span>Service Tracking</span>
            </div>
            <div className="feature-item">
              <span className="feature-icon">📊</span>
              <span>Analytics & Reports</span>
            </div>
          </div>
        </div>
      </div>

      {/* Right Panel - Auth Form */}
      <div className="auth-right-panel" ref={rightPanelRef}>
        <div className="auth-form-container">
          <Outlet />
        </div>
      </div>
    </div>
  );
};

export default AuthLayout;
