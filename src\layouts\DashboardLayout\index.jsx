import React, { useState, useEffect, useRef } from "react";
import { Outlet, useNavigate, useLocation } from "react-router-dom";
import { gsap } from "gsap";
import { 
  FaCar, FaUsers, FaTools, FaChartBar, FaCog, 
  FaHome, FaSignOutAlt, FaBars, FaTimes 
} from "react-icons/fa";
import ROUTES from "@constants/routes";

const DashboardLayout = () => {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const sidebarRef = useRef(null);
  const contentRef = useRef(null);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // Simple entrance animation
    gsap.fromTo(sidebarRef.current, {
      x: -250,
      opacity: 0
    }, {
      x: 0,
      opacity: 1,
      duration: 0.6,
      ease: "power2.out"
    });

    gsap.fromTo(contentRef.current, {
      opacity: 0,
      x: 30
    }, {
      opacity: 1,
      x: 0,
      duration: 0.8,
      ease: "power2.out",
      delay: 0.2
    });
  }, []);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
    
    gsap.to(sidebarRef.current, {
      x: sidebarOpen ? -250 : 0,
      duration: 0.3,
      ease: "power2.inOut"
    });

    gsap.to(contentRef.current, {
      marginLeft: sidebarOpen ? 0 : 250,
      duration: 0.3,
      ease: "power2.inOut"
    });
  };

  const handleLogout = () => {
    gsap.to(".dashboard-container", {
      opacity: 0,
      scale: 0.95,
      duration: 0.3,
      ease: "power2.inOut",
      onComplete: () => {
        navigate(ROUTES.SIMPLE_AUTH);
      }
    });
  };

  const menuItems = [
    { path: ROUTES.DASHBOARD, icon: FaHome, label: "Dashboard" },
    { path: ROUTES.VEHICLES, icon: FaCar, label: "Vehicles" },
    { path: ROUTES.SERVICES, icon: FaTools, label: "Services" },
    { path: ROUTES.CUSTOMERS, icon: FaUsers, label: "Customers" },
    { path: ROUTES.REPORTS, icon: FaChartBar, label: "Reports" },
    { path: ROUTES.SETTINGS, icon: FaCog, label: "Settings" }
  ];

  const isActive = (path) => location.pathname === path;

  return (
    <div className="dashboard-container">
      {/* Sidebar */}
      <aside className={`sidebar ${sidebarOpen ? 'open' : 'closed'}`} ref={sidebarRef}>
        <div className="sidebar-header">
          <div className="logo">
            <FaCar />
            <span>AutoService</span>
          </div>
          <button className="sidebar-toggle" onClick={toggleSidebar}>
            {sidebarOpen ? <FaTimes /> : <FaBars />}
          </button>
        </div>

        <nav className="sidebar-nav">
          <ul>
            {menuItems.map((item) => (
              <li key={item.path}>
                <button
                  className={`nav-item ${isActive(item.path) ? 'active' : ''}`}
                  onClick={() => navigate(item.path)}
                >
                  <item.icon />
                  <span>{item.label}</span>
                </button>
              </li>
            ))}
          </ul>
        </nav>

        <div className="sidebar-footer">
          <button className="logout-btn" onClick={handleLogout}>
            <FaSignOutAlt />
            <span>Logout</span>
          </button>
        </div>
      </aside>

      {/* Main Content */}
      <main className="main-content" ref={contentRef}>
        <Outlet />
      </main>
    </div>
  );
};

export default DashboardLayout;
