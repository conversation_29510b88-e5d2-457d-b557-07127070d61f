import React, { useState, useEffect, useRef } from "react";
import { Outlet, useNavigate, useLocation } from "react-router-dom";
import { Sidebar, Menu, MenuItem, SubMenu } from "react-pro-sidebar";
import { gsap } from "gsap";
import {
  FaCar, FaUsers, FaTools, FaChartBar, FaCog,
  FaHome, FaSignOutAlt, FaBars, FaTimes, FaCalendarAlt,
  FaWarehouse, FaUserTie, FaBell, FaSearch, FaUserCircle
} from "react-icons/fa";
import ROUTES from "@constants/routes";

const DashboardLayout = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [toggled, setToggled] = useState(false);
  const layoutRef = useRef(null);
  const headerRef = useRef(null);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // GSAP entrance animations
    const tl = gsap.timeline();

    tl.fromTo(layoutRef.current, {
      opacity: 0
    }, {
      opacity: 1,
      duration: 0.8,
      ease: "power2.out"
    });

    tl.fromTo(headerRef.current, {
      y: -50,
      opacity: 0
    }, {
      y: 0,
      opacity: 1,
      duration: 0.6,
      ease: "power2.out"
    }, "-=0.4");

  }, []);

  const handleLogout = () => {
    gsap.to(layoutRef.current, {
      opacity: 0,
      scale: 0.98,
      duration: 0.4,
      ease: "power2.inOut",
      onComplete: () => {
        navigate(ROUTES.LOGIN);
      }
    });
  };

  const isActive = (path) => location.pathname === path;

  return (
    <div className="professional-dashboard-layout" ref={layoutRef}>
      {/* Header */}
      <header className="dashboard-header" ref={headerRef}>
        <div className="header-left">
          <button
            className="sidebar-toggle-btn"
            onClick={() => setCollapsed(!collapsed)}
          >
            <FaBars />
          </button>
          <div className="header-brand">
            <FaCar className="brand-icon" />
            <span className="brand-text">AutoService Pro</span>
          </div>
        </div>

        <div className="header-center">
          <div className="search-container">
            <FaSearch className="search-icon" />
            <input
              type="text"
              placeholder="Search vehicles, customers, services..."
              className="search-input"
            />
          </div>
        </div>

        <div className="header-right">
          <button className="notification-btn">
            <FaBell />
            <span className="notification-badge">3</span>
          </button>
          <div className="user-menu">
            <FaUserCircle className="user-avatar" />
            <span className="user-name">Admin User</span>
          </div>
        </div>
      </header>

      {/* Main Layout */}
      <div className="dashboard-main">
        {/* Sidebar */}
        <Sidebar
          collapsed={collapsed}
          toggled={toggled}
          onBackdropClick={() => setToggled(false)}
          breakPoint="md"
          backgroundColor="#ffffff"
          rootStyles={{
            border: 'none',
            height: 'calc(100vh - 70px)',
            boxShadow: '2px 0 10px rgba(0,0,0,0.1)',
          }}
        >
          <Menu
            menuItemStyles={{
              button: ({ level, active }) => ({
                backgroundColor: active ? '#3498db' : 'transparent',
                color: active ? '#ffffff' : '#2c3e50',
                '&:hover': {
                  backgroundColor: active ? '#3498db' : '#f8f9fa',
                  color: active ? '#ffffff' : '#2c3e50',
                },
                padding: '12px 20px',
                margin: '4px 8px',
                borderRadius: '8px',
                transition: 'all 0.3s ease',
                fontWeight: active ? '600' : '500',
              }),
            }}
          >
            {/* Dashboard */}
            <MenuItem
              icon={<FaHome />}
              active={isActive(ROUTES.DASHBOARD)}
              onClick={() => navigate(ROUTES.DASHBOARD)}
            >
              Dashboard
            </MenuItem>

            {/* Vehicle Management */}
            <SubMenu icon={<FaCar />} label="Vehicle Management">
              <MenuItem
                active={isActive(ROUTES.VEHICLES)}
                onClick={() => navigate(ROUTES.VEHICLES)}
              >
                All Vehicles
              </MenuItem>
              <MenuItem
                active={isActive(ROUTES.INVENTORY)}
                onClick={() => navigate(ROUTES.INVENTORY)}
              >
                Parts Inventory
              </MenuItem>
            </SubMenu>

            {/* Service Management */}
            <SubMenu icon={<FaTools />} label="Service Management">
              <MenuItem
                active={isActive(ROUTES.SERVICES)}
                onClick={() => navigate(ROUTES.SERVICES)}
              >
                Service Records
              </MenuItem>
              <MenuItem
                active={isActive(ROUTES.APPOINTMENTS)}
                onClick={() => navigate(ROUTES.APPOINTMENTS)}
              >
                Appointments
              </MenuItem>
            </SubMenu>

            {/* People */}
            <SubMenu icon={<FaUsers />} label="People">
              <MenuItem
                active={isActive(ROUTES.CUSTOMERS)}
                onClick={() => navigate(ROUTES.CUSTOMERS)}
              >
                Customers
              </MenuItem>
              <MenuItem
                active={isActive(ROUTES.TECHNICIANS)}
                onClick={() => navigate(ROUTES.TECHNICIANS)}
              >
                Technicians
              </MenuItem>
            </SubMenu>

            {/* Reports */}
            <MenuItem
              icon={<FaChartBar />}
              active={isActive(ROUTES.REPORTS)}
              onClick={() => navigate(ROUTES.REPORTS)}
            >
              Reports & Analytics
            </MenuItem>

            {/* Settings */}
            <MenuItem
              icon={<FaCog />}
              active={isActive(ROUTES.SETTINGS)}
              onClick={() => navigate(ROUTES.SETTINGS)}
            >
              Settings
            </MenuItem>

            {/* Logout */}
            <MenuItem
              icon={<FaSignOutAlt />}
              onClick={handleLogout}
              style={{ marginTop: 'auto', borderTop: '1px solid #e9ecef' }}
            >
              Logout
            </MenuItem>
          </Menu>
        </Sidebar>

        {/* Content Area */}
        <main className="dashboard-content">
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default DashboardLayout;
