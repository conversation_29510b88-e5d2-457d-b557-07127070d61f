import React, { useState, useEffect, useRef } from "react";
import { Link, useNavigate } from "react-router-dom";
import { gsap } from "gsap";
import { FaEye, FaEyeSlash, FaUser, FaLock, FaCar } from "react-icons/fa";
import ROUTES from "@constants/routes";

const Login = () => {
  const [formData, setFormData] = useState({
    email: "",
    password: ""
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const cardRef = useRef(null);
  const logoRef = useRef(null);
  const formRef = useRef(null);
  const linksRef = useRef(null);
  const navigate = useNavigate();

  useEffect(() => {
    // GSAP entrance animations
    const tl = gsap.timeline({ delay: 0.2 });

    // Animate card entrance
    tl.fromTo(cardRef.current,
      { scale: 0.8, opacity: 0, y: 50 },
      { scale: 1, opacity: 1, y: 0, duration: 0.8, ease: "back.out(1.7)" }
    );

    // Animate logo
    tl.fromTo(logoRef.current,
      { y: -30, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.6, ease: "power2.out" },
      "-=0.4"
    );

    // Animate form elements
    tl.fromTo(formRef.current.children,
      { x: -30, opacity: 0 },
      { x: 0, opacity: 1, duration: 0.5, stagger: 0.1, ease: "power2.out" },
      "-=0.3"
    );

    // Animate links
    tl.fromTo(linksRef.current,
      { y: 20, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.5, ease: "power2.out" },
      "-=0.2"
    );

    // Floating animation for the car icon
    gsap.to(".car-icon", {
      y: -10,
      duration: 2,
      ease: "power2.inOut",
      repeat: -1,
      yoyo: true
    });

  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ""
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.email) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email is invalid";
    }
    
    if (!formData.password) {
      newErrors.password = "Password is required";
    } else if (formData.password.length < 6) {
      newErrors.password = "Password must be at least 6 characters";
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      // Shake animation for errors
      gsap.to(cardRef.current, {
        x: [-10, 10, -10, 10, 0],
        duration: 0.5,
        ease: "power2.out"
      });
      return;
    }

    setIsLoading(true);
    
    // Simulate API call
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Success animation
      gsap.to(cardRef.current, {
        scale: 1.05,
        duration: 0.3,
        ease: "power2.out",
        yoyo: true,
        repeat: 1
      });
      
      // Navigate to dashboard
      setTimeout(() => {
        navigate(ROUTES.DASHBOARD);
      }, 600);
      
    } catch (error) {
      console.error("Login failed:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
    
    // Icon animation
    gsap.to(".password-icon", {
      scale: 0.8,
      duration: 0.1,
      yoyo: true,
      repeat: 1,
      ease: "power2.out"
    });
  };

  return (
    <div className="auth-card" ref={cardRef}>
      <div className="auth-logo" ref={logoRef}>
        <FaCar className="car-icon" style={{ fontSize: "3rem", color: "var(--primary-orange)", marginBottom: "10px" }} />
        <h1>AutoService</h1>
        <p>Professional Vehicle Management</p>
      </div>

      <form className="auth-form" onSubmit={handleSubmit} ref={formRef}>
        <div className="form-group">
          <label className="form-label" htmlFor="email">
            <FaUser style={{ marginRight: "8px" }} />
            Email Address
          </label>
          <div className="input-group">
            <input
              type="email"
              id="email"
              name="email"
              className="form-control"
              placeholder="Enter your email"
              value={formData.email}
              onChange={handleInputChange}
              autoComplete="email"
            />
          </div>
          {errors.email && (
            <div className="error-message">
              <span>⚠️</span>
              {errors.email}
            </div>
          )}
        </div>

        <div className="form-group">
          <label className="form-label" htmlFor="password">
            <FaLock style={{ marginRight: "8px" }} />
            Password
          </label>
          <div className="input-group">
            <input
              type={showPassword ? "text" : "password"}
              id="password"
              name="password"
              className="form-control"
              placeholder="Enter your password"
              value={formData.password}
              onChange={handleInputChange}
              autoComplete="current-password"
            />
            <span 
              className="input-icon password-icon" 
              onClick={togglePasswordVisibility}
            >
              {showPassword ? <FaEyeSlash /> : <FaEye />}
            </span>
          </div>
          {errors.password && (
            <div className="error-message">
              <span>⚠️</span>
              {errors.password}
            </div>
          )}
        </div>

        <button 
          type="submit" 
          className="btn-primary"
          disabled={isLoading}
        >
          {isLoading && <div className="loading-spinner"></div>}
          {isLoading ? "Signing In..." : "Sign In"}
        </button>
      </form>

      <div className="auth-links" ref={linksRef}>
        <div style={{ marginBottom: "15px" }}>
          <Link to={ROUTES.FORGOT_PASSWORD} className="auth-link">
            Forgot your password?
          </Link>
        </div>
        
        <div className="auth-divider">
          <span>New to AutoService?</span>
        </div>
        
        <Link to={ROUTES.REGISTER} className="btn-secondary">
          Create Account
        </Link>
      </div>
    </div>
  );
};

export default Login;
