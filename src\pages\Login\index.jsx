import React, { useState, useEffect, useRef } from "react";
import { Link, useNavigate } from "react-router-dom";
import { gsap } from "gsap";
import { FaEye, FaEyeSlash, FaUser, FaLock, FaSignInAlt } from "react-icons/fa";
import ROUTES from "@constants/routes";

const Login = () => {
  const [formData, setFormData] = useState({
    email: "",
    password: ""
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const cardRef = useRef(null);
  const formRef = useRef(null);
  const navigate = useNavigate();

  useEffect(() => {
    // GSAP entrance animations
    const tl = gsap.timeline({ delay: 0.3 });

    // Animate card entrance
    tl.fromTo(cardRef.current,
      { scale: 0.9, opacity: 0, y: 30 },
      { scale: 1, opacity: 1, y: 0, duration: 0.8, ease: "power2.out" }
    );

    // Animate form elements
    tl.fromTo(formRef.current.children,
      { x: -20, opacity: 0 },
      { x: 0, opacity: 1, duration: 0.6, stagger: 0.1, ease: "power2.out" },
      "-=0.4"
    );

  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ""
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.email) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email is invalid";
    }

    if (!formData.password) {
      newErrors.password = "Password is required";
    } else if (formData.password.length < 6) {
      newErrors.password = "Password must be at least 6 characters";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      // Shake animation for errors
      gsap.to(cardRef.current, {
        x: [-8, 8, -8, 8, 0],
        duration: 0.4,
        ease: "power2.out"
      });
      return;
    }

    setIsLoading(true);

    // Simulate API call
    try {
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Success animation
      gsap.to(cardRef.current, {
        scale: 1.02,
        duration: 0.2,
        ease: "power2.out",
        yoyo: true,
        repeat: 1,
        onComplete: () => {
          navigate(ROUTES.DASHBOARD);
        }
      });

    } catch (error) {
      console.error("Login failed:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);

    // Icon animation
    gsap.to(".password-toggle", {
      scale: 0.9,
      duration: 0.1,
      yoyo: true,
      repeat: 1,
      ease: "power2.out"
    });
  };

  return (
    <div className="professional-login-card" ref={cardRef}>
      <div className="login-header">
        <h2>Welcome Back</h2>
        <p>Sign in to your AutoService Pro account</p>
      </div>

      <form className="login-form" onSubmit={handleSubmit} ref={formRef}>
        <div className="form-group">
          <label className="form-label" htmlFor="email">
            <FaUser className="label-icon" />
            Email Address
          </label>
          <div className="input-wrapper">
            <input
              type="email"
              id="email"
              name="email"
              className={`form-input ${errors.email ? 'error' : ''}`}
              placeholder="Enter your email address"
              value={formData.email}
              onChange={handleInputChange}
              autoComplete="email"
            />
          </div>
          {errors.email && (
            <div className="error-message">
              {errors.email}
            </div>
          )}
        </div>

        <div className="form-group">
          <label className="form-label" htmlFor="password">
            <FaLock className="label-icon" />
            Password
          </label>
          <div className="input-wrapper">
            <input
              type={showPassword ? "text" : "password"}
              id="password"
              name="password"
              className={`form-input ${errors.password ? 'error' : ''}`}
              placeholder="Enter your password"
              value={formData.password}
              onChange={handleInputChange}
              autoComplete="current-password"
            />
            <button
              type="button"
              className="password-toggle"
              onClick={togglePasswordVisibility}
            >
              {showPassword ? <FaEyeSlash /> : <FaEye />}
            </button>
          </div>
          {errors.password && (
            <div className="error-message">
              {errors.password}
            </div>
          )}
        </div>

        <div className="form-options">
          <Link to={ROUTES.FORGOT_PASSWORD} className="forgot-link">
            Forgot Password?
          </Link>
        </div>

        <button
          type="submit"
          className="login-button"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <div className="loading-spinner"></div>
              Signing In...
            </>
          ) : (
            <>
              <FaSignInAlt className="button-icon" />
              Sign In
            </>
          )}
        </button>
      </form>

      <div className="login-footer">
        <p>
          Don't have an account?
          <Link to={ROUTES.REGISTER} className="register-link">
            Create Account
          </Link>
        </p>
      </div>
    </div>
  );
};

export default Login;
