import React, { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { 
  FaCar, FaUsers, FaTools, FaChartBar, FaCog, 
  FaPlus, FaSearch, FaBell, FaUserCircle 
} from "react-icons/fa";

const Dashboard = () => {
  const dashboardRef = useRef(null);
  const cardsRef = useRef(null);

  useEffect(() => {
    // Simple entrance animation
    gsap.fromTo(dashboardRef.current, {
      opacity: 0
    }, {
      opacity: 1,
      duration: 0.8,
      ease: "power2.out"
    });

    // Animate cards
    gsap.fromTo(cardsRef.current.children, {
      opacity: 0,
      y: 30
    }, {
      opacity: 1,
      y: 0,
      duration: 0.6,
      stagger: 0.1,
      ease: "power2.out",
      delay: 0.3
    });
  }, []);

  const stats = [
    { title: "Total Vehicles", value: "248", icon: FaCar, color: "#FF6B35" },
    { title: "Active Services", value: "32", icon: FaTools, color: "#28A745" },
    { title: "Customers", value: "156", icon: FaUsers, color: "#007BFF" },
    { title: "Revenue", value: "$12,450", icon: FaChartBar, color: "#6F42C1" }
  ];

  const recentActivities = [
    { id: 1, action: "Vehicle service completed", vehicle: "BMW X5", time: "2 hours ago" },
    { id: 2, action: "New customer registered", customer: "John Smith", time: "4 hours ago" },
    { id: 3, action: "Service appointment scheduled", vehicle: "Toyota Camry", time: "6 hours ago" },
    { id: 4, action: "Payment received", amount: "$450", time: "1 day ago" }
  ];

  return (
    <div className="dashboard-layout" ref={dashboardRef}>
      {/* Header */}
      <header className="dashboard-header">
        <div className="header-left">
          <h1>Dashboard</h1>
          <p>Welcome back! Here's what's happening with your auto service.</p>
        </div>
        <div className="header-right">
          <div className="search-box">
            <FaSearch />
            <input type="text" placeholder="Search..." />
          </div>
          <button className="notification-btn">
            <FaBell />
            <span className="notification-badge">3</span>
          </button>
          <div className="user-profile">
            <FaUserCircle />
            <span>Admin</span>
          </div>
        </div>
      </header>

      {/* Stats Cards */}
      <div className="stats-grid" ref={cardsRef}>
        {stats.map((stat, index) => (
          <div key={index} className="stat-card">
            <div className="stat-icon" style={{ color: stat.color }}>
              <stat.icon />
            </div>
            <div className="stat-content">
              <h3>{stat.value}</h3>
              <p>{stat.title}</p>
            </div>
          </div>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="content-grid">
        {/* Quick Actions */}
        <div className="dashboard-card">
          <div className="card-header">
            <h3>Quick Actions</h3>
          </div>
          <div className="quick-actions">
            <button className="action-btn">
              <FaPlus />
              <span>Add Vehicle</span>
            </button>
            <button className="action-btn">
              <FaTools />
              <span>New Service</span>
            </button>
            <button className="action-btn">
              <FaUsers />
              <span>Add Customer</span>
            </button>
            <button className="action-btn">
              <FaChartBar />
              <span>View Reports</span>
            </button>
          </div>
        </div>

        {/* Recent Activities */}
        <div className="dashboard-card">
          <div className="card-header">
            <h3>Recent Activities</h3>
            <button className="view-all-btn">View All</button>
          </div>
          <div className="activities-list">
            {recentActivities.map((activity) => (
              <div key={activity.id} className="activity-item">
                <div className="activity-content">
                  <p className="activity-action">{activity.action}</p>
                  <p className="activity-details">
                    {activity.vehicle && <span className="vehicle">{activity.vehicle}</span>}
                    {activity.customer && <span className="customer">{activity.customer}</span>}
                    {activity.amount && <span className="amount">{activity.amount}</span>}
                  </p>
                </div>
                <span className="activity-time">{activity.time}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Service Status */}
        <div className="dashboard-card">
          <div className="card-header">
            <h3>Service Status</h3>
          </div>
          <div className="service-status">
            <div className="status-item">
              <div className="status-indicator pending"></div>
              <span>Pending Services: 8</span>
            </div>
            <div className="status-item">
              <div className="status-indicator progress"></div>
              <span>In Progress: 12</span>
            </div>
            <div className="status-item">
              <div className="status-indicator completed"></div>
              <span>Completed Today: 15</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
