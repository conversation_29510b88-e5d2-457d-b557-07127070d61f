import MainLayout from "@layouts/MainLayout";
import AuthLayout from "@layouts/AuthLayout";
import DashboardLayout from "@layouts/DashboardLayout";
import Home from "@pages/Home";
import Dashboard from "@pages/Dashboard";
import Login from "@pages/Login";
import Register from "@pages/Register";
import ForgotPassword from "@pages/ForgotPassword";
import Auth3D from "@pages/Auth3D";
import SimpleAuth from "@pages/SimpleAuth";
import React from "react";
import { BrowserRouter, Route, Routes } from "react-router-dom";
import ROUTES from "@constants/routes";

const AppRouter = () => {
  return (
    <BrowserRouter>
      <Routes>
        {/* Authentication Routes */}
        <Route element={<AuthLayout />}>
          <Route index element={<Login />} />
          <Route path={ROUTES.LOGIN} element={<Login />} />
          <Route path={ROUTES.REGISTER} element={<Register />} />
          <Route path={ROUTES.FORGOT_PASSWORD} element={<ForgotPassword />} />
        </Route>

        {/* Legacy Authentication Routes */}
        <Route path={ROUTES.SIMPLE_AUTH} element={<SimpleAuth />} />
        <Route path={ROUTES.AUTH_3D} element={<Auth3D />} />

        {/* Dashboard Routes */}
        <Route element={<DashboardLayout />}>
          <Route path={ROUTES.DASHBOARD} element={<Dashboard />} />
        </Route>

        {/* Main Application Routes */}
        <Route element={<MainLayout />}>
          <Route path={ROUTES.HOME} element={<Home />} />
        </Route>
      </Routes>
    </BrowserRouter>
  );
};

export default AppRouter;
