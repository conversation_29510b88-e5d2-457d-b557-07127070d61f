import MainLayout from "@layouts/MainLayout";
import AuthLayout from "@layouts/AuthLayout";
import DashboardLayout from "@layouts/DashboardLayout";
import Home from "@pages/Home";
import Dashboard from "@pages/Dashboard";
import Login from "@pages/Login";
import Register from "@pages/Register";
import ForgotPassword from "@pages/ForgotPassword";
import Auth3D from "@pages/Auth3D";
import SimpleAuth from "@pages/SimpleAuth";
import React from "react";
import { BrowserRouter, Route, Routes } from "react-router-dom";
import ROUTES from "@constants/routes";

const AppRouter = () => {
  return (
    <BrowserRouter>
      <Routes>
        {/* Simple Authentication Route */}
        <Route path={ROUTES.SIMPLE_AUTH} element={<SimpleAuth />} />

        {/* 3D Authentication Route */}
        <Route path={ROUTES.AUTH_3D} element={<Auth3D />} />

        {/* Unified Authentication Route */}
        <Route element={<AuthLayout />}>
    
          {/* Individual Auth Routes (Legacy) */}
          <Route path={ROUTES.LOGIN} element={<Login />} />
          <Route path={ROUTES.REGISTER} element={<Register />} />
          <Route path={ROUTES.FORGOT_PASSWORD} element={<ForgotPassword />} />
        </Route>

        {/* Main Application Routes */}
        <Route element={<MainLayout />}>
          <Route index element={<Home />} />
          <Route path={ROUTES.DASHBOARD} element={<Home />} />
        </Route>
      </Routes>
    </BrowserRouter>
  );
};

export default AppRouter;
