/* Automotive Service Theme - Orange + Black + Silver */
:root {
  --primary-orange: #FF6B35;
  --primary-black: #1A1A1A;
  --primary-silver: #C0C0C0;
  --dark-silver: #A0A0A0;
  --light-silver: #E8E8E8;
  --accent-orange: #FF8C42;
  --dark-orange: #E55A2B;
  --white: #FFFFFF;
  --error-red: #DC3545;
  --success-green: #28A745;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, var(--primary-black) 0%, #2A2A2A 100%);
  min-height: 100vh;
  overflow-x: hidden;
}

/* Clean Auth Layout Styles */
.clean-auth-layout {
  min-height: 100vh;
  display: flex;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.auth-left-panel {
  flex: 1;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  overflow: hidden;
}

.auth-right-panel {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background: #ffffff;
}

.brand-content {
  position: relative;
  z-index: 2;
  text-align: center;
  padding: 2rem;
}

.brand-header h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #ffffff;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.brand-header p {
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: 2rem;
}

.video-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.background-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0.3;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(44, 62, 80, 0.8) 0%, rgba(52, 73, 94, 0.8) 100%);
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 2rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-icon {
  font-size: 1.5rem;
}

.auth-form-container {
  width: 100%;
  max-width: 400px;
}

/* Professional Login Card */
.professional-login-card {
  background: #ffffff;
  border-radius: 16px;
  padding: 2.5rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  width: 100%;
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-header h2 {
  color: #2c3e50;
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.login-header p {
  color: #6c757d;
  font-size: 1rem;
  margin: 0;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #2c3e50;
  font-weight: 500;
  font-size: 0.9rem;
}

.label-icon {
  color: #6c757d;
  font-size: 0.9rem;
}

.input-wrapper {
  position: relative;
}

.form-input {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #f8f9fa;
  color: #2c3e50;
}

.form-input:focus {
  outline: none;
  border-color: #3498db;
  background: #ffffff;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-input.error {
  border-color: #e74c3c;
  background: #fdf2f2;
}

.password-toggle {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.password-toggle:hover {
  color: #3498db;
  background: rgba(52, 152, 219, 0.1);
}

.error-message {
  color: #e74c3c;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.form-options {
  display: flex;
  justify-content: flex-end;
  margin-top: -0.5rem;
}

.forgot-link {
  color: #3498db;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.2s ease;
}

.forgot-link:hover {
  color: #2980b9;
  text-decoration: underline;
}

.login-button {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1rem;
}

.login-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
}

.login-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.button-icon {
  font-size: 0.9rem;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.login-footer {
  text-align: center;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e9ecef;
}

.login-footer p {
  color: #6c757d;
  margin: 0;
}

.register-link {
  color: #3498db;
  text-decoration: none;
  font-weight: 500;
  margin-left: 0.5rem;
  transition: color 0.2s ease;
}

.register-link:hover {
  color: #2980b9;
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
  .clean-auth-layout {
    flex-direction: column;
  }

  .auth-left-panel {
    min-height: 40vh;
    flex: none;
  }

  .auth-right-panel {
    flex: 1;
    padding: 1.5rem;
  }

  .brand-header h1 {
    font-size: 2rem;
  }

  .features-list {
    flex-direction: row;
    justify-content: center;
    gap: 0.5rem;
  }

  .feature-item {
    flex-direction: column;
    text-align: center;
    padding: 0.5rem;
    font-size: 0.8rem;
  }

  .professional-login-card {
    padding: 2rem 1.5rem;
  }

  .login-header h2 {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .auth-right-panel {
    padding: 1rem;
  }

  .professional-login-card {
    padding: 1.5rem 1rem;
    border-radius: 12px;
  }

  .form-input {
    padding: 0.75rem;
  }

  .login-button {
    padding: 0.875rem 1.5rem;
  }
}

/* Professional Dashboard Layout */
.professional-dashboard-layout {
  min-height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

.dashboard-header {
  height: 70px;
  background: #ffffff;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 2rem;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
  z-index: 1000;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.sidebar-toggle-btn {
  background: none;
  border: none;
  color: #6c757d;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.sidebar-toggle-btn:hover {
  background: #f8f9fa;
  color: #2c3e50;
}

.header-brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.brand-icon {
  color: #3498db;
  font-size: 1.5rem;
}

.brand-text {
  font-size: 1.25rem;
  font-weight: 700;
  color: #2c3e50;
}

.header-center {
  flex: 1;
  max-width: 500px;
  margin: 0 2rem;
}

.search-container {
  position: relative;
  width: 100%;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  font-size: 0.9rem;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 2px solid #e9ecef;
  border-radius: 25px;
  background: #f8f9fa;
  color: #2c3e50;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
  background: #ffffff;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.notification-btn {
  position: relative;
  background: none;
  border: none;
  color: #6c757d;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.notification-btn:hover {
  background: #f8f9fa;
  color: #2c3e50;
}

.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  background: #e74c3c;
  color: white;
  font-size: 0.7rem;
  padding: 0.2rem 0.4rem;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  background: #f8f9fa;
  cursor: pointer;
  transition: all 0.2s ease;
}

.user-menu:hover {
  background: #e9ecef;
}

.user-avatar {
  color: #6c757d;
  font-size: 1.5rem;
}

.user-name {
  color: #2c3e50;
  font-weight: 500;
  font-size: 0.9rem;
}

.dashboard-main {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.dashboard-content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  background: #f8f9fa;
}

.auth-layout::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="15" height="15" patternUnits="userSpaceOnUse"><path d="M 15 0 L 0 0 0 15" fill="none" stroke="%23333" stroke-width="0.3" opacity="0.4"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.15;
  z-index: 1;
  animation: gridMove 20s linear infinite;
}

.auth-layout::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background:
    radial-gradient(circle at 25% 25%, rgba(255, 107, 53, 0.03) 0%, transparent 25%),
    radial-gradient(circle at 75% 75%, rgba(192, 192, 192, 0.03) 0%, transparent 25%);
  z-index: 1;
  animation: floatBackground 30s ease-in-out infinite;
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(15px, 15px); }
}

@keyframes floatBackground {
  0%, 100% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
}

.auth-container {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 450px;
  margin: 0 auto;
  padding: 20px;
}

.auth-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 107, 53, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(192, 192, 192, 0.2);
  position: relative;
  overflow: hidden;
}

.auth-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-orange) 0%, var(--accent-orange) 50%, var(--primary-orange) 100%);
  border-radius: 20px 20px 0 0;
}

/* Logo and Branding */
.auth-logo {
  text-align: center;
  margin-bottom: 30px;
}

.auth-logo h1 {
  color: var(--primary-black);
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 5px;
  background: linear-gradient(135deg, var(--primary-orange) 0%, var(--dark-orange) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.auth-logo p {
  color: var(--dark-silver);
  font-size: 0.9rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Form Styles */
.auth-form {
  width: 100%;
}

.form-group {
  margin-bottom: 25px;
  position: relative;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  color: var(--primary-black);
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-control {
  width: 100%;
  padding: 15px 20px;
  border: 2px solid var(--light-silver);
  border-radius: 12px;
  font-size: 1rem;
  background: var(--white);
  color: var(--primary-black);
  transition: all 0.3s ease;
  position: relative;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-orange);
  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
  transform: translateY(-2px);
  animation: glow 3s infinite;
}

.form-control::placeholder {
  color: var(--dark-silver);
  font-weight: 400;
}

/* Input Icons */
.input-group {
  position: relative;
}

.input-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--dark-silver);
  font-size: 1.2rem;
  cursor: pointer;
  transition: color 0.3s ease;
}

.input-icon:hover {
  color: var(--primary-orange);
}

/* Buttons */
.btn-primary {
  width: 100%;
  padding: 15px;
  background: linear-gradient(135deg, var(--primary-orange) 0%, var(--dark-orange) 100%);
  border: none;
  border-radius: 12px;
  color: var(--white);
  font-size: 1.1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(255, 107, 53, 0.3);
  animation: glow 2s infinite;
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-secondary {
  width: 100%;
  padding: 15px;
  background: transparent;
  border: 2px solid var(--primary-silver);
  border-radius: 12px;
  color: var(--primary-black);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 15px;
}

.btn-secondary:hover {
  background: var(--primary-silver);
  color: var(--white);
  transform: translateY(-2px);
}

/* Links */
.auth-links {
  text-align: center;
  margin-top: 25px;
}

.auth-link {
  color: var(--primary-orange);
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
}

.auth-link:hover {
  color: var(--dark-orange);
  text-decoration: none;
}

.auth-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-orange);
  transition: width 0.3s ease;
}

.auth-link:hover::after {
  width: 100%;
}

/* Divider */
.auth-divider {
  text-align: center;
  margin: 30px 0;
  position: relative;
}

.auth-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--light-silver);
}

.auth-divider span {
  background: var(--white);
  padding: 0 20px;
  color: var(--dark-silver);
  font-weight: 500;
  position: relative;
  z-index: 1;
}

/* Error Messages */
.error-message {
  color: var(--error-red);
  font-size: 0.85rem;
  margin-top: 5px;
  display: flex;
  align-items: center;
  gap: 5px;
}

/* Success Messages */
.success-message {
  color: var(--success-green);
  font-size: 0.85rem;
  margin-top: 5px;
  display: flex;
  align-items: center;
  gap: 5px;
}

/* Loading Spinner */
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid var(--white);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes glow {
  0% { box-shadow: 0 0 5px rgba(255, 107, 53, 0.3); }
  50% { box-shadow: 0 0 20px rgba(255, 107, 53, 0.6); }
  100% { box-shadow: 0 0 5px rgba(255, 107, 53, 0.3); }
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .auth-container {
    padding: 15px;
  }

  .auth-card {
    padding: 30px 25px;
    border-radius: 15px;
  }

  .auth-logo h1 {
    font-size: 2rem;
  }

  .form-control {
    padding: 12px 15px;
  }

  .btn-primary,
  .btn-secondary {
    padding: 12px;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .auth-card {
    padding: 25px 20px;
  }

  .auth-logo h1 {
    font-size: 1.8rem;
  }
}

/* Animation Classes for GSAP */
.fade-in {
  opacity: 0;
}

.slide-up {
  transform: translateY(30px);
  opacity: 0;
}

.scale-in {
  transform: scale(0.9);
  opacity: 0;
}

.slide-left {
  transform: translateX(-30px);
  opacity: 0;
}

.slide-right {
  transform: translateX(30px);
  opacity: 0;
}

/* Simple Auth Layout */
.simple-auth-layout {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-black) 0%, #2A2A2A 100%);
  padding: 20px;
}

.simple-auth-layout .auth-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(192, 192, 192, 0.2);
  width: 100%;
  max-width: 450px;
  position: relative;
}

.simple-auth-layout .auth-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-orange) 0%, var(--accent-orange) 100%);
  border-radius: 20px 20px 0 0;
}

.simple-auth-layout .auth-logo {
  text-align: center;
  margin-bottom: 30px;
}

.simple-auth-layout .auth-logo h1 {
  color: var(--primary-black);
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 5px;
  background: linear-gradient(135deg, var(--primary-orange) 0%, var(--dark-orange) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.simple-auth-layout .auth-logo p {
  color: var(--dark-silver);
  font-size: 0.9rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.simple-auth-layout .form-header {
  text-align: center;
  margin-bottom: 30px;
}

.simple-auth-layout .form-header h2 {
  color: var(--primary-black);
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 8px;
}

.simple-auth-layout .form-header p {
  color: var(--dark-silver);
  font-size: 1rem;
  font-weight: 400;
}

.simple-auth-layout .form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 0;
}

.simple-auth-layout .form-footer {
  text-align: center;
  margin-top: 25px;
}

.simple-auth-layout .form-footer p {
  color: var(--dark-silver);
  font-size: 0.95rem;
  margin-bottom: 0;
}

.simple-auth-layout .link-button {
  background: none;
  border: none;
  color: var(--primary-orange);
  text-decoration: none;
  font-weight: 600;
  cursor: pointer;
  margin-left: 8px;
  transition: all 0.3s ease;
}

.simple-auth-layout .link-button:hover {
  color: var(--dark-orange);
  text-decoration: underline;
}

/* Responsive */
@media (max-width: 768px) {
  .simple-auth-layout .auth-card {
    padding: 30px 25px;
    border-radius: 15px;
  }

  .simple-auth-layout .auth-logo h1 {
    font-size: 2rem;
  }

  .simple-auth-layout .form-row {
    grid-template-columns: 1fr;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .simple-auth-layout {
    padding: 15px;
  }

  .simple-auth-layout .auth-card {
    padding: 25px 20px;
  }

  .simple-auth-layout .auth-logo h1 {
    font-size: 1.8rem;
  }
}

/* Dashboard Layout */
.dashboard-container {
  display: flex;
  min-height: 100vh;
  background: #f8f9fa;
}

/* Sidebar */
.sidebar {
  width: 250px;
  background: var(--primary-black);
  color: white;
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: 1000;
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
}

.sidebar.closed {
  transform: translateX(-250px);
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sidebar-header .logo {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-orange);
}

.sidebar-toggle {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background 0.3s ease;
}

.sidebar-toggle:hover {
  background: rgba(255, 255, 255, 0.1);
}

.sidebar-nav {
  flex: 1;
  padding: 20px 0;
}

.sidebar-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-nav li {
  margin-bottom: 5px;
}

.nav-item {
  width: 100%;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  padding: 15px 20px;
  text-align: left;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1rem;
}

.nav-item:hover {
  background: rgba(255, 107, 53, 0.1);
  color: var(--primary-orange);
}

.nav-item.active {
  background: var(--primary-orange);
  color: white;
}

.sidebar-footer {
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.logout-btn {
  width: 100%;
  background: none;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 12px;
  justify-content: center;
}

.logout-btn:hover {
  background: rgba(220, 53, 69, 0.1);
  border-color: #dc3545;
  color: #dc3545;
}

/* Main Content */
.main-content {
  flex: 1;
  margin-left: 250px;
  transition: margin-left 0.3s ease;
}

/* Dashboard Layout */
.dashboard-layout {
  padding: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e9ecef;
}

.header-left h1 {
  color: var(--primary-black);
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 5px;
}

.header-left p {
  color: #6c757d;
  font-size: 1rem;
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-box svg {
  position: absolute;
  left: 15px;
  color: #6c757d;
  font-size: 1rem;
}

.search-box input {
  padding: 10px 15px 10px 45px;
  border: 1px solid #e9ecef;
  border-radius: 25px;
  background: white;
  font-size: 0.9rem;
  width: 250px;
  transition: all 0.3s ease;
}

.search-box input:focus {
  outline: none;
  border-color: var(--primary-orange);
  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

.notification-btn {
  position: relative;
  background: none;
  border: none;
  color: #6c757d;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 10px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.notification-btn:hover {
  background: rgba(255, 107, 53, 0.1);
  color: var(--primary-orange);
}

.notification-badge {
  position: absolute;
  top: 5px;
  right: 5px;
  background: #dc3545;
  color: white;
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--primary-black);
  font-weight: 600;
}

.user-profile svg {
  font-size: 1.8rem;
  color: var(--primary-orange);
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  font-size: 2.5rem;
  padding: 15px;
  background: rgba(255, 107, 53, 0.1);
  border-radius: 12px;
}

.stat-content h3 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-black);
  margin-bottom: 5px;
}

.stat-content p {
  color: #6c757d;
  font-size: 0.9rem;
  margin: 0;
  font-weight: 500;
}

/* Content Grid */
.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.dashboard-card {
  background: white;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  overflow: hidden;
}

.card-header {
  padding: 20px 25px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  color: var(--primary-black);
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.view-all-btn {
  background: none;
  border: none;
  color: var(--primary-orange);
  font-weight: 600;
  cursor: pointer;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.view-all-btn:hover {
  color: var(--dark-orange);
}

/* Quick Actions */
.quick-actions {
  padding: 25px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.action-btn {
  background: rgba(255, 107, 53, 0.1);
  border: 1px solid rgba(255, 107, 53, 0.2);
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  color: var(--primary-orange);
  font-weight: 600;
}

.action-btn:hover {
  background: var(--primary-orange);
  color: white;
  transform: translateY(-2px);
}

.action-btn svg {
  font-size: 1.5rem;
}

/* Activities List */
.activities-list {
  padding: 25px;
}

.activity-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 15px 0;
  border-bottom: 1px solid #f8f9fa;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-action {
  font-weight: 600;
  color: var(--primary-black);
  margin-bottom: 5px;
}

.activity-details {
  font-size: 0.9rem;
  color: #6c757d;
  margin: 0;
}

.activity-details .vehicle,
.activity-details .customer,
.activity-details .amount {
  color: var(--primary-orange);
  font-weight: 600;
}

.activity-time {
  font-size: 0.8rem;
  color: #6c757d;
  white-space: nowrap;
}

/* Service Status */
.service-status {
  padding: 25px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  font-weight: 500;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.status-indicator.pending {
  background: #ffc107;
}

.status-indicator.progress {
  background: #007bff;
}

.status-indicator.completed {
  background: #28a745;
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-250px);
  }

  .main-content {
    margin-left: 0;
  }

  .dashboard-layout {
    padding: 20px;
  }

  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }

  .header-right {
    width: 100%;
    justify-content: space-between;
  }

  .search-box input {
    width: 200px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .content-grid {
    grid-template-columns: 1fr;
  }

  .quick-actions {
    grid-template-columns: 1fr;
  }
}

/* Unified Auth Card Styles */
.unified-auth-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-radius: 24px;
  padding: 50px;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 107, 53, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(192, 192, 192, 0.15);
  position: relative;
  overflow: hidden;
  width: 100%;
  max-width: 480px;
  margin: 0 auto;
}

.unified-auth-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(90deg,
    var(--primary-orange) 0%,
    var(--accent-orange) 25%,
    var(--primary-orange) 50%,
    var(--accent-orange) 75%,
    var(--primary-orange) 100%);
  border-radius: 24px 24px 0 0;
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Form Container */
.form-container {
  position: relative;
  min-height: 400px;
  overflow: hidden;
}

.auth-form-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  opacity: 0;
  pointer-events: none;
  transition: none;
}

.auth-form-wrapper.active {
  opacity: 1;
  pointer-events: all;
  position: relative;
}

.auth-form-wrapper.hidden {
  opacity: 0;
  pointer-events: none;
  position: absolute;
}

/* Form Headers */
.form-header {
  text-align: center;
  margin-bottom: 35px;
}

.form-header h2 {
  color: var(--primary-black);
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 8px;
  background: linear-gradient(135deg, var(--primary-orange) 0%, var(--dark-orange) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.form-header p {
  color: var(--dark-silver);
  font-size: 1rem;
  font-weight: 500;
}

/* Form Row for Side-by-Side Fields */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 0;
}

/* Form Links */
.form-links {
  text-align: center;
  margin: 20px 0;
}

.link-button {
  background: none;
  border: none;
  color: var(--primary-orange);
  text-decoration: none;
  font-weight: 600;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  padding: 8px 0;
}

.link-button:hover {
  color: var(--dark-orange);
  transform: translateY(-1px);
}

.link-button::after {
  content: '';
  position: absolute;
  bottom: 2px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-orange);
  transition: width 0.3s ease;
}

.link-button:hover::after {
  width: 100%;
}

.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: rgba(255, 107, 53, 0.1);
  transform: translateY(-2px);
}

/* Success Content */
.success-content {
  text-align: center;
  padding: 20px 0;
}

.success-icon {
  font-size: 4.5rem;
  color: var(--success-green);
  margin-bottom: 25px;
  animation: pulse 2s infinite;
}

.success-content h2 {
  color: var(--primary-black);
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 15px;
}

.success-content p {
  color: var(--dark-silver);
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 30px;
}

.success-actions {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* Enhanced Button Styles */
.btn-primary,
.btn-secondary {
  position: relative;
  overflow: hidden;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.4s ease;
}

.btn-primary::before,
.btn-secondary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent);
  transition: left 0.6s ease;
}

.btn-primary:hover::before,
.btn-secondary:hover::before {
  left: 100%;
}

/* Enhanced Form Controls */
.form-control {
  border: 2px solid var(--light-silver);
  border-radius: 14px;
  padding: 16px 22px;
  font-size: 1rem;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
  transition: all 0.4s ease;
}

.form-control:focus {
  border-color: var(--primary-orange);
  box-shadow:
    0 0 0 4px rgba(255, 107, 53, 0.1),
    0 8px 25px rgba(255, 107, 53, 0.15);
  transform: translateY(-3px);
  background: rgba(255, 255, 255, 1);
}

/* Enhanced Input Groups */
.input-group {
  position: relative;
}

.input-icon {
  position: absolute;
  right: 18px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--dark-silver);
  font-size: 1.3rem;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 5px;
  border-radius: 50%;
}

.input-icon:hover {
  color: var(--primary-orange);
  background: rgba(255, 107, 53, 0.1);
  transform: translateY(-50%) scale(1.1);
}

/* Enhanced Error Messages */
.error-message {
  color: var(--error-red);
  font-size: 0.85rem;
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
  animation: slideInError 0.3s ease;
}

@keyframes slideInError {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Enhancements */
@media (max-width: 768px) {
  .unified-auth-card {
    padding: 35px 30px;
    border-radius: 20px;
    margin: 15px;
  }

  .form-header h2 {
    font-size: 1.7rem;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .form-container {
    min-height: 350px;
  }
}

@media (max-width: 480px) {
  .unified-auth-card {
    padding: 30px 25px;
    border-radius: 18px;
  }

  .form-header h2 {
    font-size: 1.5rem;
  }

  .success-actions {
    gap: 12px;
  }

  .form-control {
    padding: 14px 18px;
    font-size: 0.95rem;
  }
}