import Axios from "axios";
import { store } from "@store/store";

const axiosInstance = Axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_URL + '/api',
});

axiosInstance.interceptors.request.use((config) => {
  const token = store.getState().user.token; // Access token from Redux store
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

axiosInstance.interceptors.response.use((response) => {
  if (response.status === 200 && !response.headers.get("content-disposition")) {
    return response.data;
  }
  return response;
});

export default axiosInstance;
